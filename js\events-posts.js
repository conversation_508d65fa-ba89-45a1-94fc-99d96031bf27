// Gallery Events Data and Management
// ===================================
// This file contains all event data for the gallery system
// Each event has its own collection of photos
// To add new events, simply add them to the events array below

// Events data - each event contains its own photo collection
const events = [
    {
        id: 5,
        title: "Fajr Community Breakfast",
        date: "2025-05-25",
        location: "Al Rahman Masjid",
        description: "Every year we invite Muslims from all over Auckland to listen to Quran classes during Fajr time and then have a community breakfast together as one Ummah. This blessed gathering brings our community together in the early morning hours, starting with spiritual nourishment through Quranic teachings followed by sharing a meal that strengthens the bonds of brotherhood and sisterhood among believers.",
        coverImage: "./event-pages/event-images/FB2025 (13).webp",
        photoCount: 30,
        slug: "fajr-community-breakfast",
        category: "Community",
        photos: [
            { id: 1, image: "event-images/FB2025 (1).webp" },
            { id: 2, image: "event-images/FB2025 (2).webp" },
            { id: 3, image: "event-images/FB2025 (3).webp" },
            { id: 4, image: "event-images/FB2025 (4).webp" },
            { id: 5, image: "event-images/FB2025 (5).webp" },
            { id: 6, image: "event-images/FB2025 (6).webp" },
            { id: 7, image: "event-images/FB2025 (7).webp" },
            { id: 8, image: "event-images/FB2025 (8).webp" },
            { id: 9, image: "event-images/FB2025 (9).webp" },
            { id: 10, image: "event-images/FB2025 (10).webp" },
            { id: 11, image: "event-images/FB2025 (11).webp" },
            { id: 12, image: "event-images/FB2025 (12).webp" },
            { id: 13, image: "event-images/FB2025 (13).webp" },
            { id: 14, image: "event-images/FB2025 (14).webp" },
            { id: 15, image: "event-images/FB2025 (15).webp" },
            { id: 16, image: "event-images/FB2025 (16).webp" },
            { id: 17, image: "event-images/FB2025 (17).webp" },
            { id: 18, image: "event-images/FB2025 (18).webp" },
            { id: 19, image: "event-images/FB2025 (19).webp" },
            { id: 20, image: "event-images/FB2025 (20).webp" },
            { id: 21, image: "event-images/FB2025 (21).webp" },
            { id: 22, image: "event-images/FB2025 (22).webp" },
            { id: 23, image: "event-images/FB2025 (23).webp" },
            { id: 24, image: "event-images/FB2025 (24).webp" },
            { id: 25, image: "event-images/FB2025 (25).webp" },
            { id: 26, image: "event-images/FB2025 (26).webp" },
            { id: 27, image: "event-images/FB2025 (27).webp" },
            { id: 28, image: "event-images/FB2025 (28).webp" },
            { id: 29, image: "event-images/FB2025 (29).webp" },
            { id: 30, image: "event-images/FB2025 (30).webp" }
        ]
    },
    {
        id: 1,
        title: "Arabic Grammar Batch 8 Class",
        date: "2025-04-15",
        location: "NZICT, Mount Roskill",
        description: "Photos of students from Batch 8 studying Arabic Grammar in their class under the guidance of Ustad Mamoon Saeed.",
        coverImage: "./event-pages/event-images/8thbatch (3).webp",
        photoCount: 13,
        slug: "arabic-grammar-batch-8-class",
        category: "Class",
        photos: [
            { id: 1, image: "event-images/8thbatch (1).webp" },
            { id: 2, image: "event-images/8thbatch (2).webp" },
            { id: 3, image: "event-images/8thbatch (3).webp" },
            { id: 4, image: "event-images/8thbatch (4).webp" },
            { id: 5, image: "event-images/8thbatch (5).webp" },
            { id: 6, image: "event-images/8thbatch (6).webp" },
            { id: 7, image: "event-images/8thbatch (7).webp" },
            { id: 8, image: "event-images/8thbatch (8).webp" },
            { id: 9, image: "event-images/8thbatch (9).webp" },
            { id: 10, image: "event-images/8thbatch (10).webp" },
            { id: 11, image: "event-images/8thbatch (11).webp" },
            { id: 12, image: "event-images/8thbatch (12).webp" },
            { id: 13, image: "event-images/8thbatch (13).webp" }
        ]
    },
    {
        id: 2,
        title: "Arabic Conference 2024",
        date: "2024-12-02",
        location: "Mount Albert War Memorial Hall",
        description: "An inspiring Arabic Conference bringing together scholars, students, and community members to explore the beauty and depth of the Arabic language. The conference features lectures on Arabic grammar, literature, and its significance in Islamic studies, along with interactive workshops and networking opportunities for Arabic language enthusiasts.",
        coverImage: "./event-pages/event-images/AC2024 (55).webp",
        photoCount: 55,
        slug: "arabic-conference-2024",
        category: "Conference",
        photos: [
            { id: 1, image: "event-images/AC2024 (1).webp" },
            { id: 2, image: "event-images/AC2024 (2).webp" },
            { id: 3, image: "event-images/AC2024 (3).webp" },
            { id: 4, image: "event-images/AC2024 (4).webp" },
            { id: 5, image: "event-images/AC2024 (5).webp" },
            { id: 6, image: "event-images/AC2024 (6).webp" },
            { id: 7, image: "event-images/AC2024 (7).webp" },
            { id: 8, image: "event-images/AC2024 (8).webp" },
            { id: 9, image: "event-images/AC2024 (9).webp" },
            { id: 10, image: "event-images/AC2024 (10).webp" },
            { id: 11, image: "event-images/AC2024 (11).webp" },
            { id: 12, image: "event-images/AC2024 (12).webp" },
            { id: 13, image: "event-images/AC2024 (13).webp" },
            { id: 14, image: "event-images/AC2024 (14).webp" },
            { id: 15, image: "event-images/AC2024 (15).webp" },
            { id: 16, image: "event-images/AC2024 (16).webp" },
            { id: 17, image: "event-images/AC2024 (17).webp" },
            { id: 18, image: "event-images/AC2024 (18).webp" },
            { id: 19, image: "event-images/AC2024 (19).webp" },
            { id: 20, image: "event-images/AC2024 (20).webp" },
            { id: 21, image: "event-images/AC2024 (21).webp" },
            { id: 22, image: "event-images/AC2024 (22).webp" },
            { id: 23, image: "event-images/AC2024 (23).webp" },
            { id: 24, image: "event-images/AC2024 (24).webp" },
            { id: 25, image: "event-images/AC2024 (25).webp" },
            { id: 26, image: "event-images/AC2024 (26).webp" },
            { id: 27, image: "event-images/AC2024 (27).webp" },
            { id: 28, image: "event-images/AC2024 (28).webp" },
            { id: 29, image: "event-images/AC2024 (29).webp" },
            { id: 30, image: "event-images/AC2024 (30).webp" },
            { id: 31, image: "event-images/AC2024 (31).webp" },
            { id: 32, image: "event-images/AC2024 (32).webp" },
            { id: 33, image: "event-images/AC2024 (33).webp" },
            { id: 34, image: "event-images/AC2024 (34).webp" },
            { id: 35, image: "event-images/AC2024 (35).webp" },
            { id: 36, image: "event-images/AC2024 (36).webp" },
            { id: 37, image: "event-images/AC2024 (37).webp" },
            { id: 38, image: "event-images/AC2024 (38).webp" },
            { id: 39, image: "event-images/AC2024 (39).webp" },
            { id: 40, image: "event-images/AC2024 (40).webp" },
            { id: 41, image: "event-images/AC2024 (41).webp" },
            { id: 42, image: "event-images/AC2024 (42).webp" },
            { id: 43, image: "event-images/AC2024 (43).webp" },
            { id: 44, image: "event-images/AC2024 (44).webp" },
            { id: 45, image: "event-images/AC2024 (45).webp" },
            { id: 46, image: "event-images/AC2024 (46).webp" },
            { id: 47, image: "event-images/AC2024 (47).webp" },
            { id: 48, image: "event-images/AC2024 (48).webp" },
            { id: 49, image: "event-images/AC2024 (49).webp" },
            { id: 50, image: "event-images/AC2024 (50).webp" },
            { id: 51, image: "event-images/AC2024 (51).webp" },
            { id: 52, image: "event-images/AC2024 (52).webp" },
            { id: 53, image: "event-images/AC2024 (53).webp" },
            { id: 54, image: "event-images/AC2024 (54).webp" },
            { id: 55, image: "event-images/AC2024 (55).webp" }
        ]
    },
    {
        id: 3,
        title: "Young Minds Tarbiyah Session",
        date: "2025-05-25",
        location: "Community Center",
        description: "Inspiring tarbiyah sessions designed for young minds to strengthen their Islamic foundation. Our youth gather to listen to enlightening lectures by renowned scholar Nouman Ali Khan, receive spiritual guidance from their dedicated ustad, and benefit from special guest speakers including accomplished authors. These sessions focus on character development, Islamic values, and building a strong connection with faith while addressing contemporary challenges faced by Muslim youth.",
        coverImage: "./event-pages/event-images/youth (9).webp",
        photoCount: 16,
        slug: "young-minds-tarbiyah-session",
        category: "program",
        photos: [
            { id: 1, image: "event-images/youth (1).webp" },
            { id: 2, image: "event-images/youth (2).webp" },
            { id: 3, image: "event-images/youth (3).webp" },
            { id: 4, image: "event-images/youth (4).webp" },
            { id: 5, image: "event-images/youth (5).webp" },
            { id: 6, image: "event-images/youth (6).webp" },
            { id: 7, image: "event-images/youth (7).webp" },
            { id: 8, image: "event-images/youth (8).webp" },
            { id: 9, image: "event-images/youth (9).webp" },
            { id: 10, image: "event-images/youth (10).webp" },
            { id: 11, image: "event-images/youth (11).webp" },
            { id: 12, image: "event-images/youth (12).webp" },
            { id: 13, image: "event-images/youth (13).webp" },
            { id: 14, image: "event-images/youth (14).webp" },
            { id: 15, image: "event-images/youth (15).webp" },
            { id: 16, image: "event-images/youth (16).webp" }
        ]
    },
    {
        id: 4,
        title: "Weekly Quran Classes",
        date: "2025-05-25",
        location: "Al Rahman Masjid",
        description: "Collection of photos from our ongoing weekly Quran lecture series covering the tafseer of the Quran.",
        coverImage: "./event-pages/event-images/QC2025 (5).webp",
        photoCount: 8,
        slug: "weekly-quran-classes",
        category: "class",
        photos: [
            { id: 1, image: "event-images/QC2025 (1).webp" },
            { id: 2, image: "event-images/QC2025 (2).webp" },
            { id: 3, image: "event-images/QC2025 (3).webp" },
            { id: 4, image: "event-images/QC2025 (4).webp" },
            { id: 5, image: "event-images/QC2025 (5).webp" },
            { id: 6, image: "event-images/QC2025 (6).webp" },
            { id: 7, image: "event-images/QC2025 (7).webp" },
            { id: 8, image: "event-images/QC2025 (8).webp" }
        ]
    }
];

// Sort events by date (most recent first)
const sortedEvents = [...events].sort((a, b) => new Date(b.date) - new Date(a.date));

// Calculate total photos across all events
const totalPhotos = events.reduce((total, event) => total + event.photoCount, 0);

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { events, sortedEvents, totalPhotos };
}
