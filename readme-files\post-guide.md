# News Posts - Quick Admin Guide

## 🚀 How to Add a New Post

### Step 1: Open the File
- Open `js/news-posts.js` (in js folder)

### Step 2: Add Your Post
Add this anywhere in the `communityPosts` array (around line 10):

```javascript
{
    id: 10,                                    // Use next available number (currently 10)
    title: 'Your Post Title',                  // Keep under 60 characters
    description: 'Your full post description', // 2-3 sentences minimum
    date: '2025-01-20',                       // Format: YYYY-MM-DD
    image: 'news-images/your-image.jpg'       // Place image in news-images/ folder
}
```

### Step 3: Save the File
- Save `js/news-posts.js`
- Posts automatically sort by ID (highest = newest)

## 📋 Requirements

### Required Fields (All Must Be Included):
- **id**: Next available number (currently use 10)
- **title**: Post headline
- **description**: Full post content
- **date**: YYYY-MM-DD format
- **image**: news-images/filename.jpg

### Image Requirements:
- **Folder**: Place in `news-images/` folder
- **Format**: JPG, PNG, or WebP
- **Size**: Under 500KB
- **Ratio**: Square (1:1) works best

## ⚠️ Common Issues

**Posts not showing?**
- Check for missing commas between posts
- Ensure all fields are included
- Verify image file exists in news-images/ folder

**Wrong order?**
- Use higher ID number for newer posts
- Current posts use IDs 1-9, so use 10+ for new posts

## 📁 File Locations
- **Edit posts**: `js/news-posts.js` (js folder)
- **Add images**: `news-images/` folder
- **This guide**: `post-guide.md`

---
**Quick Reference**: Next ID to use = **10**
