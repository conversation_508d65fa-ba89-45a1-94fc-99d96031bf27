<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - <PERSON>vants of the Quran | Islamic Education</title>
    <meta name="description" content="Enroll in our Quranic Arabic Grammar Learning Programme. Free classes for male adults starting June 2025. Enhance your connection to the Quran through linguistic tools.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://khuddam.co.nz/">
    <meta property="og:title" content="Enrollment Open: Quranic Arabic Grammar Learning Programme">
    <meta property="og:description" content="Free Quranic Arabic Grammar classes for male adults starting June 2025. Register now to secure your place!">
    <meta property="og:image" content="images/enrollment-post.png">
    <meta property="og:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://khuddam.co.nz/">
    <meta name="twitter:title" content="Enrollment Open: Quranic Arabic Grammar Learning Programme">
    <meta name="twitter:description" content="Free Quranic Arabic Grammar classes for male adults starting June 2025. Register now to secure your place!">
    <meta name="twitter:image" content="images/enrollment-post.png">
    <meta name="twitter:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Critical CSS loaded directly for faster rendering -->
    <link rel="stylesheet" href="css/critical.css">

    <!-- Preload hero video for faster loading -->
    <link rel="preload" href="./30fps.mp4" as="video" type="video/mp4">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- External CSS resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap">


    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://khuddam.co.nz/#website",
                "url": "https://khuddam.co.nz/",
                "name": "Khuddam - Servants of the Quran",
                "description": "Islamic Education and Quranic Arabic Grammar Learning",
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": "https://khuddam.co.nz/?s={search_term_string}",
                    "query-input": "required name=search_term_string"
                },
                "inLanguage": "en"
            },
            {
                "@type": "Organization",
                "@id": "https://khuddam.co.nz/#organization",
                "name": "Khuddam Ul Quran",
                "url": "https://khuddam.co.nz/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://khuddam.co.nz/images/old-logo-border.png",
                    "width": 300,
                    "height": 100,
                    "caption": "Khuddam - Servants of the Quran"
                },
                "sameAs": [
                    "https://chat.whatsapp.com/GqCM92Qp90cJA1ZDZHGmNH"
                ],
                "email": "<EMAIL>",
                "telephone": "+64 22 161 5574",
                "address": {
                    "@type": "PostalAddress",
                    "streetAddress": "60 Stoddard Road",
                    "addressLocality": "Mount Roskill",
                    "addressRegion": "Auckland",
                    "postalCode": "1041",
                    "addressCountry": "NZ"
                },
                "location": {
                    "@type": "Place",
                    "geo": {
                        "@type": "GeoCoordinates",
                        "latitude": -36.9056247,
                        "longitude": 174.7252606
                    }
                }
            },
            {
                "@type": "EducationalOrganization",
                "@id": "https://khuddam.co.nz/#educationalorganization",
                "name": "Khuddam Ul Quran",
                "url": "https://khuddam.co.nz/",
                "description": "Founded in 2020, Khuddam-Al-Quran meaning 'Servants of the Quran' is a community of dedicated Muslims committed to serving the Quran and the Ummah through linguistic education and spiritual development.",
                "alumni": {
                    "@type": "Person",
                    "name": "Khuddam Alumni"
                }
            },
            {
                "@type": "Course",
                "@id": "https://khuddam.co.nz/#course",
                "name": "Quranic Arabic Grammar Learning Programme",
                "description": "Free Quranic Arabic Grammar classes for male adults starting June 2025. Enhance your connection to the Quran through linguistic tools.",
                "provider": {
                    "@type": "Organization",
                    "name": "Khuddam Ul Quran",
                    "sameAs": "https://khuddam.co.nz/"
                },
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "NZD",
                    "availability": "https://schema.org/InStock",
                    "validFrom": "2024-07-01"
                },
                "hasCourseInstance": {
                    "@type": "CourseInstance",
                    "name": "Batch 2025",
                    "courseMode": "OFFLINE",
                    "location": {
                        "@type": "Place",
                        "name": "Khuddam Ul Quran",
                        "address": {
                            "@type": "PostalAddress",
                            "streetAddress": "60 Stoddard Road",
                            "addressLocality": "Mount Roskill",
                            "addressRegion": "Auckland",
                            "postalCode": "1041",
                            "addressCountry": "NZ"
                        }
                    },
                    "startDate": "2025-02-01"
                }
            },
            {
                "@type": "LocalBusiness",
                "@id": "https://khuddam.co.nz/#localbusiness",
                "name": "Khuddam Ul Quran",
                "image": "https://khuddam.co.nz/images/old-logo-border.png",
                "url": "https://khuddam.co.nz/",
                "telephone": "+64 22 161 5574",
                "email": "<EMAIL>",
                "address": {
                    "@type": "PostalAddress",
                    "streetAddress": "60 Stoddard Road",
                    "addressLocality": "Mount Roskill",
                    "addressRegion": "Auckland",
                    "postalCode": "1041",
                    "addressCountry": "NZ"
                },
                "geo": {
                    "@type": "GeoCoordinates",
                    "latitude": -36.9056247,
                    "longitude": 174.7252606
                },
                "openingHoursSpecification": [
                    {
                        "@type": "OpeningHoursSpecification",
                        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                        "opens": "18:00",
                        "closes": "22:00"
                    },
                    {
                        "@type": "OpeningHoursSpecification",
                        "dayOfWeek": ["Sunday"],
                        "opens": "06:00",
                        "closes": "08:00"
                    }
                ]
            },
            {
                "@type": "BreadcrumbList",
                "@id": "https://khuddam.co.nz/#breadcrumblist",
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "item": {
                            "@id": "https://khuddam.co.nz/",
                            "name": "Home"
                        }
                    }
                ]
            }
        ]
    }
    </script>
</head>
<body class="bg-black font-sans text-dark">
    <!-- Loading Screen -->
    <div class="loader-container" id="loader">
        <div class="book-animation">
            <i class="fas fa-book-open"></i>
        </div>
        <p class="arabic-text font-bold text-3xl">بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ</p>
        <div class="loader-text">Opening the gates of knowledge...</div>
        <div class="loader-progress">
            <div class="progress-bar" id="progress"></div>
        </div>
    </div>

    <div id="main-content">
    <!-- Header with Navigation -->
    <header class="bg-dark/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/">
                        <img src="images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-primary font-medium hover:text-white transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
                    <a href="registration-form.html" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
                </div>

                <!-- Mobile Navigation Toggle -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-dark bg-opacity-95 z-50">
                <!-- Close button -->
                <button id="mobile-menu-close" class="absolute top-6 right-6 text-white hover:text-primary">
                    <i class="fas fa-times text-2xl"></i>
                </button>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-primary text-2xl font-medium hover:text-white transition">Home</a>
                        <a href="about.html" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="news.html" class="text-white text-2xl font-medium hover:text-primary transition">News</a>
                        <a href="events.html" class="text-white text-2xl font-medium hover:text-primary transition">Events</a>
                        <a href="contact-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Contact</a>
                        <a href="registration-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Register Now</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative overflow-hidden hero-section bg-black pt-16" id="hero">
        <div class="absolute inset-0 w-full h-full">
            <video class="absolute inset-0 min-w-full min-h-full object-cover" autoplay loop muted playsinline preload="auto" >
                <source src="./30fps.mp4" type="video/mp4">
                <!-- Fallback image if video doesn't load -->
                <img src="images/image1.webp" alt="Islamic background" class="absolute inset-0 min-w-full min-h-full object-cover" loading="lazy">
            </video>
            <!-- Video Overlay -->
            <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>

        <!-- Content Container -->
        <div class="relative h-full flex items-center justify-center">
            <div class="container mx-auto px-4">
                <div class="flex flex-col items-center justify-center text-center max-w-3xl mx-auto">
                    <!-- Center-aligned Content -->
                    <div class="w-full">
                        <h1 class="font-arabic text-7xl md:text-8xl mb-3 text-white">خُدَّام</h1>
                        <h1 class="font-arabic text-7xl md:text-8xl mb-20 text-primary quran-text" id="quran-text">القُرآن</h1>

                        <h4 class="text-3xl md:text-5xl mb-6 font-bold text-gray-300">
                            Serving the Quran, <br> Serving the Ummah
                        </h4>
                        <p class="max-w-2xl mx-auto text-lg mb-10 text-gray-300">
                            Enhance your connection to the Quran through linguistic tools and community building.
                        </p>

                        <div class="flex flex-wrap gap-4 mt-4 justify-center">
                            <a href="registration-form.html" class="border border-primary text-primary hover:bg-primary hover:text-black px-6 py-3 font-medium transition-all duration-300 w-48 text-center">
                                Enroll Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Why Learn Arabic Section -->
    <section id="why-learn-arabic" class="relative overflow-hidden py-24 bg-white -mt-1">
        <div class="absolute inset-0 w-full h-full">
            <!-- Quran Background Image -->
            <img src="./images/quran-photo.webp" alt="Open Quran with Arabic text - decorative background" class="absolute inset-0 w-full h-full object-cover opacity-5">
        </div>

        <!-- Content Container -->
        <div class="relative container mx-auto px-4">
            <div class="max-w-5xl mx-auto">
                <!-- Left side - Large Text -->
                <div class="text-center mb-10" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-6xl font-bold text-gray-800 mb-4">WHY LEARN ARABIC?</h2>
                    <div class="line w-32 h-2 bg-primary mx-auto mb-6"></div>
                    <h3 class="text-2xl font-bold text-gray-600 mb-6">Understanding Arabic grammar is essential to connect with the words of Allah (Subhanahu wa Ta'ala)</h3>
                    <p class="text-gray-600 text-lg mb-6 max-w-3xl mx-auto">
                        The Arabic language has a rich structure that conveys meaning not just through words, but through grammatical patterns that reveal subtle nuances and profound insights.
                    </p>

                <!-- Redesigned Quote Section - More Visible and Focused -->
                <div class="quote-section my-12 max-w-3xl mx-auto">
                    <!-- First Quote -->
                    <div class="backdrop-blur-md p-8 shadow-md border-l-4 border-primary mb-8 transform hover:scale-105 transition-transform duration-300" data-aos="fade-right" data-aos-duration="500">
                        <div class="flex flex-col items-center">
                            <p class="font-arabic font-bold text-3xl md:text-4xl text-primary text-center mb-4">خَيْرُكُمْ مَنْ تَعَلَّمَ الْقُرْآنَ وَعَلَّمَهُ</p>

                            <p class="text-gray-900 text-xl italic text-center font-medium">
                                "The best among you are those who learn the Quran and teach it."
                            </p>
                            <p class="text-primary font-medium mt-2">- Sahih al-Bukhari</p>
                        </div>
                    </div>

                    <!-- Second Quote -->
                    <div class="backdrop-blur-md p-8 shadow-md border-l-4 border-primary mb-8 transform hover:scale-105 transition-transform duration-300" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                        <div class="flex flex-col items-center">
                            <p class="font-arabic font-bold text-3xl md:text-4xl text-primary text-center mb-4">إِنَّا أَنزَلْنَاهُ قُرْآنًا عَرَبِيًّا لَّعَلَّكُمْ تَعْقِلُونَ</p>

                            <p class="text-gray-900 text-xl italic text-center font-medium">
                                "Indeed, We have sent it down as an Arabic Quran that you might understand."
                            </p>
                            <p class="text-primary font-medium mt-2">- Quran 12:2</p>
                        </div>
                    </div>

                    <!-- Third Quote -->
                    <div class="backdrop-blur-md p-8  shadow-md border-l-4 border-primary transform hover:scale-105 transition-transform duration-300" data-aos="fade-right" data-aos-duration="500" data-aos-delay="400">
                        <div class="flex flex-col items-center">
                            <p class="font-arabic font-bold text-3xl md:text-4xl text-primary text-center mb-4">وَلَقَدْ يَسَّرْنَا الْقُرْآنَ لِلذِّكْرِ فَهَلْ مِن مُّدَّكِرٍ</p>

                            <p class="text-gray-900 text-xl italic text-center font-medium">
                                "And We have certainly made the Quran easy for remembrance, so is there any who will remember?"
                            </p>
                            <p class="text-primary font-medium mt-2">- Quran 54:17</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </section>

    <!-- Enrollment Section -->
    <!-- <section id="enrollment" class="relative py-20 bg-dark text-white overflow-hidden">
        <div class="absolute inset-0 gradient-animation"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <div class="inline-block bg-white px-6 py-2 mb-12">
                    <span class="text-primary font-bold text-sm">ANNOUNCEMENT</span>
                </div>

                <h4 class="font-arabic text-2xl sm:text-4xl font-bold text-primary mb-6">اَلسَّلَامُ عَلَيْكُمْ وَ رَحْمَةُ اللهِ وَ بَرَكَاتُهُ</h4>
                <h1 class="text-4xl font-bold mb-6 text-primary">
                    Enrollment for Quranic Arabic Grammar Learning Classes
                </h1>
                <h2 class="text-2xl md:text-3xl mb-4 text-white">
                    BATCH 2025 IS OPEN
                </h2>

                <p class="text-lg mb-4">
                    Dear Brothers, by Permission and Favor of Allah SWT, we are <span class="font-arabic">إِنْ شَاءَ اللهُ </span> planning to start a
                    <span class="font-bold text-primary">new batch of Quranic Arabic Grammar Learning</span> for male adults.
                </p>
            </div>
        </div>
    </section> -->

    <!-- Class Details Section -->
    <section id="class-details" class="py-20 bg-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 w-full h-full">
            <img src="./images/quranpage.webp" alt="Quran page with Arabic calligraphy - decorative background" class="absolute inset-0 w-full h-full object-cover opacity-40">
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-5xl mx-auto">
                <!-- Section Header with Arabic Styling -->
                <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-4xl font-bold mb-4 relative inline-block">
                        <span class="relative z-10">Class Details</span>
                        <span class="absolute -bottom-2 left-0 w-full h-1 bg-primary"></span>
                    </h2>
                </div>

                <!-- Timeline Point - Starting Date -->
                <div class="relative z-10 mb-16" data-aos="zoom-in" data-aos-duration="500" data-aos-delay="200">
                    <div class="flex items-center justify-center mb-4">
                        <div class="bg-primary w-16 h-16 rounded-full flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110">
                            <i class="fas fa-calendar-day text-black text-2xl"></i>
                        </div>
                    </div>
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Starting June 2025</h3>
                        <p class="text-gray-600 font-arabic font-bold text-3xl">إِنْ شَاءَ اللهُ</p>
                    </div>
                </div>

                <!-- Interactive Cards Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <!-- Card 1: Eligibility -->
                    <div class="bg-white border-t-4 border-primary shadow-lg" data-aos="fade-up" data-aos-duration="500" data-aos-delay="100">
                        <div class="p-6 h-48 flex flex-col">
                            <div class="w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                                <i class="fas fa-user-tie text-primary text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-center mb-4">Who Can Join</h3>
                            <div class="text-center">
                                <p class="text-gray-700 mb-2"><strong>Male adults</strong></p>
                            </div>
                            <div class="flex-grow"></div>
                        </div>
                        <div class="bg-gray-50 px-6 py-4">
                            <div class="flex items-center justify-center text-center">
                                <span class="text-primary mr-2"><i class="fas fa-info-circle"></i></span>
                                <span class="text-gray-600 text-sm">16 years and above</span>
                            </div>
                        </div>
                    </div>

                    <!-- Card 2: Schedule -->
                    <div class="bg-white border-t-4 border-primary shadow-lg" data-aos="fade-up" data-aos-duration="500" data-aos-delay="300">
                        <div class="p-6 h-48 flex flex-col">
                            <div class="w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                                <i class="fas fa-clock text-primary text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-center mb-4">Class Schedule</h3>
                            <div class="text-center">
                                <p class="text-gray-700 mb-2"><strong>Once a week</strong></p>
                                <p class="text-gray-600">Duration: 1 hour</p>
                            </div>
                            <div class="flex-grow"></div>
                        </div>
                        <div class="bg-gray-50 px-6 py-4">
                            <div class="flex items-center justify-center text-center">
                                <span class="text-primary mr-2"><i class="fas fa-calendar-alt"></i></span>
                                <span class="text-gray-600 text-sm">Specific day to be announced</span>
                            </div>
                        </div>
                    </div>

                    <!-- Card 3: Location -->
                    <div class="bg-white border-t-4 border-primary shadow-lg" data-aos="fade-up" data-aos-duration="500" data-aos-delay="500">
                        <div class="p-6 h-48 flex flex-col">
                            <div class="w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                                <i class="fas fa-map-marker-alt text-primary text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-center mb-4">NZICT</h3>
                            <div class="text-center">
                                <p class="text-gray-600">60 Stoddard Road</p>
                                <p class="text-gray-600">Mount Roskill</p>
                            </div>
                            <div class="flex-grow"></div>
                        </div>
                        <div class="bg-gray-50 px-6 py-4">
                            <div class="flex items-center justify-center text-center">
                                <span class="text-primary mr-2"><i class="fas fa-users"></i></span>
                                <span class="text-gray-600 text-sm">Face to face interactive</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="bg-gradient-to-r from-dark to-dark/90 p-8 shadow-xl mb-16" data-aos="fade-up" data-aos-duration="500">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <!-- Feature 1 -->
                        <div class="text-center transform transition-transform duration-300 hover:scale-105">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-gift text-black text-2xl"></i>
                            </div>
                            <h4 class="text-white font-bold mb-1">Free of Cost</h4>
                            <p class="text-white/70 text-sm">100% Free Program</p>
                        </div>

                        <!-- Feature 2 -->
                        <div class="text-center transform transition-transform duration-300 hover:scale-105">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-language text-black text-2xl"></i>
                            </div>
                            <h4 class="text-white font-bold mb-1">Teaching Medium</h4>
                            <p class="text-white/70 text-sm">Urdu / Hindi / English</p>
                        </div>

                        <!-- Feature 3 -->
                        <div class="text-center transform transition-transform duration-300 hover:scale-105">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book-reader text-black text-2xl"></i>
                            </div>
                            <h4 class="text-white font-bold mb-1">Structured Learning</h4>
                            <p class="text-white/70 text-sm">Progressive Curriculum</p>
                        </div>

                        <!-- Feature 4 -->
                        <div class="text-center transform transition-transform duration-300 hover:scale-105">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-certificate text-black text-2xl"></i>
                            </div>
                            <h4 class="text-white font-bold mb-1">Certification</h4>
                            <p class="text-white/70 text-sm">Upon Completion</p>
                        </div>
                    </div>
                </div>

                <!-- Registration CTA -->
                <div class="text-center relative" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
                    <div class="absolute -top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-10 bg-primary/50"></div>
                    <div class="inline-block relative">
                        <div class="absolute -top-3 -left-3 w-16 h-16 bg-primary/10 animate-pulse"></div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4 relative">Ready to Begin Your Journey?</h3>
                    </div>
                    <p class="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
                        Registration is required for all participants. Secure your place in our upcoming batch.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <a href="registration-form.html" class="bg-primary hover:bg-primary/90 text-black font-medium px-10 py-4 text-lg transition-all duration-300 inline-flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <span>Register Now</span>
                            <i class="fas fa-arrow-right ml-3"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary/5"></div>
    </section>

    <!-- Benefits of Learning Arabic - Dark Theme Section -->
    <section id="benefits" class="py-16 bg-dark text-white relative overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-5xl mx-auto">
                <div class="text-center mb-10" data-aos="fade-up" data-aos-duration="500">
                    <h2 class="text-3xl font-bold text-primary mb-4">Benefits of Learning Arabic</h2>
                    <p class="text-white/80 text-lg max-w-3xl mx-auto">
                        Our Arabic grammar learning classes are designed to make learning accessible, engaging, and effective from beginners to advanced students. Learning Arabic grammar offers numerous benefits:
                    </p>
                </div>

                <div class="w-full">
                    <!-- Cards in a horizontal row on larger screens, vertical on mobile -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- Enhanced Comprehension Card -->
                        <div class="border border-gray-800 bg-black/30 p-8 hover:shadow-lg hover:border-primary/50 transition-all duration-300" data-aos="fade-right" data-aos-duration="500">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary/20 rounded-full p-3 mr-3">
                                    <i class="fas fa-book text-primary text-xl"></i>
                                </div>
                                <span class="text-primary text-3xl font-bold">01</span>
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-primary">Enhanced Comprehension</h3>
                            <p class="text-white/80">Gain deeper understanding of Quranic verses and Islamic texts in their original form without relying on translations.</p>
                        </div>

                        <!-- Spiritual Connection Card -->
                        <div class="border border-gray-800 bg-black/30 p-8 hover:shadow-lg hover:border-primary/50 transition-all duration-300" data-aos="fade-up" data-aos-duration="500" data-aos-delay="200">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary/20 rounded-full p-3 mr-3">
                                    <i class="fas fa-heart text-primary text-xl"></i>
                                </div>
                                <span class="text-primary text-3xl font-bold">02</span>
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-primary">Spiritual Connection</h3>
                            <p class="text-white/80">Develop a more profound connection with your faith through direct engagement with sacred texts in their authentic language.</p>
                        </div>

                        <!-- Intellectual Growth Card -->
                        <div class="border border-gray-800 bg-black/30 p-8 hover:shadow-lg hover:border-primary/50 transition-all duration-300" data-aos="fade-left" data-aos-duration="500" data-aos-delay="400">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary/20 rounded-full p-3 mr-3">
                                    <i class="fas fa-brain text-primary text-xl"></i>
                                </div>
                                <span class="text-primary text-3xl font-bold">03</span>
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-primary">Intellectual Growth</h3>
                            <p class="text-white/80">Strengthen critical thinking and analytical skills while exploring the rich linguistic traditions of classical Arabic.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <!-- Gallery Section -->
    <section id="gallery" class="py-24 bg-white -mt-1 relative overflow-hidden">
        <div class="container mx-auto px-4 relative">
            <!-- Section header -->
            <div class="text-center mb-16 max-w-3xl mx-auto" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-4xl font-bold text-primary mb-4">Gallery</h2>
                <div class="w-24 h-1 bg-primary mx-auto mb-8"></div>
            </div>

            <!-- Gallery Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">

                <!-- Gallery Item 1 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="100">
                    <img src="images/arabicclass1.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 1" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass1.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 2 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="200">
                    <img src="images/arabicclass2.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 2" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass2.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 3 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="300">
                    <img src="images/arabicclass3.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 3" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass3.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 4 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="400">
                    <img src="images/arabicclass4.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 4" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass4.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 5 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="100">
                    <img src="images/arabicclass5.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 5" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass5.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 6 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="200">
                    <img src="images/arabicclass6.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 6" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass6.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 7 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="300">
                    <img src="images/arabicclass7.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 7" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass7.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 8 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="400">
                    <img src="images/arabicclass8.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 8" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass8.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 9 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="100">
                    <img src="images/arabicclass9.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 9" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass9.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 10 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="200">
                    <img src="images/arabicclass10.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 10" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass10.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 11 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="300">
                    <img src="images/arabicclass11.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 11" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass11.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Gallery Item 12 -->
                <div class="gallery-item group relative overflow-hidden shadow-md" data-aos="fade-up" data-aos-delay="400">
                    <img src="images/arabicclass12.webp" class="w-full h-64 object-cover rounded-sm transition-transform duration-300 group-hover:scale-110" alt="Arabic Class 12" loading="lazy">
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <a href="images/arabicclass12.webp" class="lightbox-link text-white">
                            <i class="fas fa-search-plus text-2xl"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Counter Section -->
    <section id="impact" class="py-20 relative overflow-hidden">
        <!-- Background image -->
        <div class="absolute inset-0 w-full h-full">
            <img src="images/kaaba2.webp" alt="Kaaba in Mecca with pilgrims - background for impact section" class="absolute inset-0 min-w-full min-h-full object-cover" >
            <!-- Dark overlay - reduced opacity to make Kaaba more visible -->
            <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-4xl font-bold text-white mb-4">Our Impact</h2>
                <p class="text-xl text-white/80">Khuddam Ul Quran Proudly Serving Muslims Around Aotearoa, New Zealand and Beyond</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-4xl mx-auto">
                <!-- Counter 1 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="121">0</div>
                    <p class="text-white text-xl">Active Students</p>
                </div>

                <!-- Counter 2 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="276">0</div>
                    <p class="text-white text-xl">Graduates</p>
                </div>

                <!-- Counter 3 -->
                <div class="text-center">
                    <div class="text-6xl md:text-7xl font-bold text-primary mb-4 counter-value" data-count="5">0</div>
                    <p class="text-white text-xl">Countries Reached</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gray-50 text-gray-800 relative overflow-hidden">
        <img src="./images/quran-photo.webp" alt="Quran Background" class="absolute inset-0 w-full h-full object-cover opacity-20">

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-8 sm:mb-12" data-aos="fade-up" data-aos-duration="500">
                <h2 class="text-3xl sm:text-4xl font-bold mb-3 sm:mb-4 text-primary-black">Impact Stories</h2>
                <div class="w-16 sm:w-24 h-1 bg-primary mx-auto mb-4 sm:mb-8"></div>
                <p class="max-w-3xl mx-auto text-base sm:text-lg md:text-xl text-gray-900 px-4">
                    Hear from our students about how Khuddam's Arabic Grammar program has transformed their understanding of the Quran
                </p>
            </div>

            <!-- Crossfade with Quote Icon Testimonial Carousel -->
            <div class="relative max-w-3xl mx-auto" id="testimonial-container" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
                <!-- Fixed Quote Icon -->
                <div class="absolute top-10 left-1/2 transform -translate-x-1/2 z-20">
                    <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                        <i class="fas fa-quote-right text-primary text-3xl"></i>
                    </div>
                </div>

                <!-- Testimonials Wrapper -->
                <div class="bg-white/40 backdrop-blur-[10px] p-6 sm:p-8 border-2 border-primary shadow-lg rounded-sm min-h-[350px] relative overflow-hidden">
                    <!-- Testimonial 1 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "Before joining Khuddam's Arabic Grammar program, I struggled to understand the deeper meanings of the Quran. The structured approach to learning grammar has transformed my ability to connect with Allah's words."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Ahmed Ibrahim</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The Arabic Grammar course at Khuddam has completely changed how I read the Quran. Understanding the difference between Ism, Fi'l, and Harf has given me insights I never thought possible."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Waqar Raja</h4>
                                <p class="text-gray-500 text-sm">Intermediate Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "As someone who tried to learn Arabic grammar multiple times before, Khuddam's approach is truly unique. The focus on Quranic application rather than abstract rules makes all the difference."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Zaka Chohan</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 4 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "Learning Arabic grammar with Khuddam has been a transformative journey. The way they connect grammar rules directly to Quranic verses makes the learning process meaningful and spiritually enriching."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Saad Mahmood</h4>
                                <p class="text-gray-500 text-sm">Advanced Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 5 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "I've tried many Arabic courses before, but Khuddam's approach to grammar is exceptional. They focus on practical application rather than overwhelming theory. Now I can actually understand the grammatical structure of Quranic verses."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Farhan Qureshi</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 6 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The step-by-step approach to Arabic grammar at Khuddam has been perfect for me. Each lesson builds on the previous one in a logical way, making complex concepts accessible."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Imran Siddiqui</h4>
                                <p class="text-gray-500 text-sm">Intermediate Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 7 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "What sets Khuddam's Arabic grammar program apart is how they connect every rule to practical examples from the Quran. This approach has not only improved my understanding of Arabic but has deepened my connection with the Quran itself."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Yasir Malik</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Graduate</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 8 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "The Arabic grammar classes have given me a new perspective on the Quran. I can now appreciate the linguistic beauty and precision that I was missing before. It's like seeing the Quran in high definition."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Umar Farooq</h4>
                                <p class="text-gray-500 text-sm">Advanced Arabic Grammar Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 9 -->
                    <div class="testimonial-slide absolute inset-0 opacity-0 transition-opacity duration-1000 ease-in-out p-6 sm:p-8" data-active="false">
                        <div class="testimonial-content pt-16 flex flex-col items-center">
                            <p class="testimonial-text italic text-gray-700 mb-6 text-base sm:text-lg text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-500">
                                "I was hesitant to start learning Arabic grammar at my age, but Khuddam's program made it accessible and enjoyable. The community of learners and the supportive teachers have made this journey a blessing in my life."
                            </p>
                            <div class="testimonial-author-container  pt-4 mt-auto text-center opacity-0 transform translate-y-4 transition-all duration-1000 delay-800">
                                <h4 class="font-bold text-lg sm:text-xl text-primary-black">Abdul Rahman</h4>
                                <p class="text-gray-500 text-sm">Arabic Grammar Program Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- No progress bar -->
                </div>

                <!-- Navigation Arrows -->
                <button class="absolute top-1/2 left-0 -translate-y-1/2 -translate-x-1 sm:-translate-x-4 md:-translate-x-8 bg-primary text-black w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center focus:outline-none shadow-lg z-10 hover:bg-primary/90 transition-all duration-300" id="prev-testimonial">
                    <i class="fas fa-chevron-left text-base sm:text-lg"></i>
                </button>
                <button class="absolute top-1/2 right-0 -translate-y-1/2 translate-x-1 sm:translate-x-4 md:translate-x-8 bg-primary text-black w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center focus:outline-none shadow-lg z-10 hover:bg-primary/90 transition-all duration-300" id="next-testimonial">
                    <i class="fas fa-chevron-right text-base sm:text-lg"></i>
                </button>

                <!-- Dots Indicator -->
                <div class="flex justify-center mt-8 sm:mt-10 space-x-2 sm:space-x-3">
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full shadow-sm transition-all duration-300" data-index="0"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="1"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="2"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="3"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="4"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="5"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="6"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="7"></button>
                    <button class="w-3 h-3 sm:w-4 sm:h-4 bg-gray-300 rounded-full shadow-sm transition-all duration-300" data-index="8"></button>
                </div>
            </div>
        </div>
    </section>

    <!-- Community & Events CTA Section -->
    <section id="community-events-cta" class="py-20 bg-dark relative overflow-hidden">
        <img src="./images/image1.webp" alt="Quran Background" class="absolute inset-0 w-full h-full object-cover opacity-50">
            <div class="container mx-auto px-4 relative z-10">
                <div class="max-w-6xl mx-auto">
                    <!-- Section Header -->
                    <div class="text-center mb-16" data-aos="fade-up" data-aos-duration="500">
                        <h2 class="text-4xl font-bold text-white mb-4">Explore Our Community</h2>
                        <div class="w-24 h-1 bg-primary mx-auto mb-6"></div>
                        <p class="text-lg text-white/80 max-w-3xl mx-auto">
                            Stay connected with our vibrant community and discover upcoming events that strengthen our bonds in faith and learning.
                        </p>
                    </div>

                    <!-- CTA Cards Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        <!-- Community CTA Card -->
                        <div class="bg-white border-2 border-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-aos="fade-right" data-aos-duration="500" data-aos-delay="100">
                            <div class="p-8 text-center">
                                <!-- Icon -->
                                <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-users text-primary text-3xl"></i>
                                </div>

                                <!-- Content -->
                                <h3 class="text-2xl font-bold text-gray-800 mb-4">News Posts</h3>
                                <p class="text-gray-600 mb-6 leading-relaxed">
                                    Recent updates on our Arabic classes,  and community announcements that keep our ummah connected.
                                </p>

                                <!-- Features List -->
                                <div class="space-y-2 mb-8">
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Latest Class Updates</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Dawah Activities</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Community Announcements</span>
                                    </div>
                                </div>

                                <!-- CTA Button -->
                                <a href="news.html" class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-medium px-8 py-3 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                    <span>View News</span>
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Events CTA Card -->
                        <div class="bg-white border-2 border-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                            <div class="p-8 text-center">
                                <!-- Icon -->
                                <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-calendar-alt text-primary text-3xl"></i>
                                </div>

                                <!-- Content -->
                                <h3 class="text-2xl font-bold text-gray-800 mb-4">Event Gallery</h3>
                                <p class="text-gray-600 mb-6 leading-relaxed">
                                    Browse through beautiful moments captured from our Arabic classes, community gatherings, and special Islamic events.
                                </p>

                                <!-- Features List -->
                                <div class="space-y-2 mb-8">
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Class Photo Galleries</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Event Highlights</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-check text-primary mr-2"></i>
                                        <span>Community Memories</span>
                                    </div>
                                </div>

                                <!-- CTA Button -->
                                <a href="events.html" class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-medium px-8 py-3 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                    <span>View Events</span>
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary text-dark -mt-1">
        <div class="container mx-auto px-4">
            <div class="max-w-5xl mx-auto flex flex-col md:flex-row items-center justify-between">
                <div class="mb-8 md:mb-0 text-center md:text-left" data-aos="fade-right" data-aos-duration="500">
                    <h2 class="text-3xl md:text-4xl font-bold mb-4">Have Questions?</h2>
                    <p class="text-lg md:text-xl text-dark/80 max-w-2xl">
                        Reach out to us with any questions about our Arabic Grammar program or other Islamic education opportunities.
                    </p>
                </div>
                <div data-aos="fade-left" data-aos-duration="500" data-aos-delay="200">
                    <a href="contact-form.html" class="inline-block bg-dark hover:bg-dark/90 text-white font-medium px-8 py-4 transition-all duration-300">
                        <span class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="footer" class="bg-dark/95 shadow-md text-white -mt-1">
        <div class="container mx-auto px-4 py-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Logos -->
                <div class="mb-6 md:mb-0 flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="mr-2">
                            <img src="./images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12" >
                        </div>
                    </div>
                    <div class="flex items-center">
                        <a href="/">
                            <img src="./images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12" >
                        </a>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                    <a href="/" class="text-primary font-medium hover:text-white transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="registration-form.html" class="text-white font-medium hover:text-primary transition">Register Now</a>
                    <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
                </div>

                <!-- Social Media -->
                <div class="flex space-x-4">
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                    <div class="mt-4 md:mt-0">
                        <ul class="flex space-x-6 text-sm">
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Developer Credits -->
                <div class="mt-4 pt-4 border-t border-white/5">
                    <div class="text-center">
                        <p class="text-white/50 text-xs">
                            Website developed by
                            <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                Kiwiorbit
                            </a>
                            | Contact:
                            <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                +64 22 325 9094
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js" defer></script>
    </div><!-- End of main-content -->

    <!-- Loader Script -->
    <script src="js/loader.js" defer></script>

    <!-- Counter Animation Script -->
    <script>
        // Function to animate counter
        function animateCounter(element, duration) {
            const target = parseInt(element.getAttribute('data-count'));
            const start = 0;
            const increment = Math.ceil(target / (duration / 16)); // 16ms is roughly one frame at 60fps
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                } else {
                    element.textContent = current;
                }
            }, 16);
        }

        // Initialize counter animations
        document.addEventListener('DOMContentLoaded', function() {
            let countersAnimated = false;
            const counters = document.querySelectorAll('.counter-value');

            // Function to start counter animations
            function startCounterAnimations() {
                if (!countersAnimated) {
                    countersAnimated = true;
                    counters.forEach(counter => {
                        counter.classList.add('animated');
                        animateCounter(counter, 7000); // 7 seconds duration
                    });
                }
            }

            // Use Intersection Observer to detect when counter section is visible
            const counterSection = document.getElementById('impact');
            if (counterSection && 'IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startCounterAnimations();
                            // Once counters are started, no need to observe anymore
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.2 }); // Trigger when 20% visible

                observer.observe(counterSection);
            }

            // Method 3: Fallback to scroll event if all else fails
            window.addEventListener('scroll', function scrollHandler() {
                const counterSection = document.getElementById('impact');
                if (counterSection) {
                    const rect = counterSection.getBoundingClientRect();
                    const windowHeight = window.innerHeight || document.documentElement.clientHeight;

                    if (rect.top <= windowHeight * 0.8 && rect.bottom >= windowHeight * 0.2) {
                        startCounterAnimations();
                        window.removeEventListener('scroll', scrollHandler);
                    }
                }
            });

            // Method 4: Last resort - trigger after a delay if user has scrolled
            let hasScrolled = false;
            window.addEventListener('scroll', function() {
                hasScrolled = true;
            }, { once: true });

            // If user has scrolled but counters haven't animated after 5 seconds, force animation
            setTimeout(() => {
                if (hasScrolled && !countersAnimated) {
                    startCounterAnimations();
                }
            }, 5000);
        });
    </script>

    <!-- Testimonial Slider Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.testimonial-slide');
            const dots = document.querySelectorAll('[data-index]');
            const prevButton = document.getElementById('prev-testimonial');
            const nextButton = document.getElementById('next-testimonial');
            let currentIndex = 0;
            let interval;
            const duration = 7000; // 7 seconds per slide
            let isTransitioning = false;

            // No progress bar or transition-width utility needed

            // Function to show a specific slide
            function showSlide(index) {
                if (isTransitioning) return;
                isTransitioning = true;

                // Hide current slide
                const currentSlide = document.querySelector('.testimonial-slide[data-active="true"]');
                if (currentSlide) {
                    // Hide text and author first
                    const currentText = currentSlide.querySelector('.testimonial-text');
                    const currentAuthor = currentSlide.querySelector('.testimonial-author-container');

                    currentText.classList.add('opacity-0', 'translate-y-4');
                    currentAuthor.classList.add('opacity-0', 'translate-y-4');

                    // Then fade out the slide after a short delay
                    setTimeout(() => {
                        currentSlide.classList.remove('opacity-100');
                        currentSlide.setAttribute('data-active', 'false');

                        // Show new slide after current slide fades out
                        setTimeout(() => {
                            // Show new slide
                            slides.forEach((slide, i) => {
                                if (i === index) {
                                    slide.classList.add('opacity-100');
                                    slide.setAttribute('data-active', 'true');

                                    // Animate in text and author
                                    const text = slide.querySelector('.testimonial-text');
                                    const author = slide.querySelector('.testimonial-author-container');

                                    setTimeout(() => {
                                        text.classList.remove('opacity-0', 'translate-y-4');

                                        setTimeout(() => {
                                            author.classList.remove('opacity-0', 'translate-y-4');
                                            isTransitioning = false;
                                        }, 500);
                                    }, 500);
                                }
                            });
                        }, 500);
                    }, 300);
                } else {
                    // First slide (no current active slide)
                    slides[index].classList.add('opacity-100');
                    slides[index].setAttribute('data-active', 'true');

                    // Animate in text and author
                    const text = slides[index].querySelector('.testimonial-text');
                    const author = slides[index].querySelector('.testimonial-author-container');

                    setTimeout(() => {
                        text.classList.remove('opacity-0', 'translate-y-4');

                        setTimeout(() => {
                            author.classList.remove('opacity-0', 'translate-y-4');
                            isTransitioning = false;
                        }, 500);
                    }, 500);
                }

                // Update dots
                dots.forEach((dot, i) => {
                    if (i === index) {
                        dot.classList.remove('bg-gray-300');
                        dot.classList.add('bg-primary');
                    } else {
                        dot.classList.remove('bg-primary');
                        dot.classList.add('bg-gray-300');
                    }
                });

                currentIndex = index;
            }

            // Start automatic slideshow
            function startSlideshow() {
                clearInterval(interval);
                interval = setInterval(() => {
                    if (!isTransitioning) {
                        const nextSlide = (currentIndex + 1) % slides.length;
                        showSlide(nextSlide);
                    }
                }, duration);
            }

            // Initialize slideshow
            showSlide(0);
            startSlideshow();

            // Event listeners for next and previous buttons
            if (nextButton) {
                nextButton.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const nextSlide = (currentIndex + 1) % slides.length;
                        showSlide(nextSlide);
                        startSlideshow(); // Reset the interval
                    }
                });
            }

            if (prevButton) {
                prevButton.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const prevSlide = (currentIndex - 1 + slides.length) % slides.length;
                        showSlide(prevSlide);
                        startSlideshow(); // Reset the interval
                    }
                });
            }

            // Event listeners for dots
            dots.forEach(dot => {
                dot.addEventListener('click', function() {
                    if (!isTransitioning) {
                        const index = parseInt(this.getAttribute('data-index'));
                        if (index !== currentIndex) {
                            showSlide(index);
                            startSlideshow(); // Reset the interval
                        }
                    }
                });
            });

            // No pause on hover functionality
        });
    </script>

    <!-- Enrollment Modal with Fade-in Animation -->
    <div id="enrollmentModal" class="fixed inset-0 z-[100] hidden opacity-0 transition-opacity duration-700">
        <!-- Modal Backdrop -->
        <div id="modalBackdrop" class="absolute inset-0 backdrop-blur-[2px]"></div>

        <!-- Modal Content -->
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[95%] sm:w-[90%] md:w-[85%] max-w-2xl opacity-0 translate-y-8 transition-all duration-700 ease-out" id="modalContent">
            <!-- Main Modal Container -->
            <div class="bg-dark border-2 border-primary overflow-hidden">
                <!-- Close Button -->
                <button id="closeModal" class="absolute top-2 right-2 sm:top-4 sm:right-4 text-white hover:text-primary z-20 bg-black/50 w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Top Banner -->
                <div class="bg-primary py-2 sm:py-3 px-4 sm:px-6 text-center relative">
                    <h2 class="text-black text-lg sm:text-xl font-bold uppercase tracking-wider">Enrollment Closed</h2>
                </div>

                <!-- Main Content Area -->
                <div class="p-4 sm:p-6 md:p-8">
                    <!-- Header -->
                    <h2 class="text-2xl sm:text-3xl font-bold text-white text-center mb-3 sm:mb-4">
                        <span class="text-primary">REGISTRATION FOR ARABIC BATCH-10 </span><br>
                        <span class="text-xl sm:text-2xl">IS NOW CLOSED</span>
                    </h2>

                    <!-- Course Description -->
                    <div class="mb-4 sm:mb-8">
                        <p class="text-white/90 text-sm sm:text-base text-center sm:text-justify mb-4 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-2 sm:px-4">
                            Experience a unique approach to understand Quranic Arabic that combines traditional and modern teaching methodologies. This carefully structured program breaks down complex grammatical concepts into accessible lessons, creating a clear path from basic understanding to advanced textual analysis. The Goal of this course to develop the skills to independently explore the linguistic treasures of the Quran and Hadith, opening new dimensions of meaning and guidance.
                        </p>
                    </div>

                    <!-- Course Details with Icons - Exact Match -->
                    <div class="flex justify-center mb-4 sm:mb-6">
                        <div class="grid grid-cols-5 gap-1 sm:gap-2 w-full max-w-md px-1 sm:px-2">
                            <!-- Detail 1: Time -->
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-clock text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">1 Hour</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Duration</p>
                            </div>

                            <!-- Detail 2: Days -->
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-calendar-alt text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">Once</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Per Week</p>
                            </div>

                            <!-- Detail 3: Free Course -->
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-gift text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">100% Free</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">No Cost</p>
                            </div>

                            <!-- Detail 4: Language -->
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-globe text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">English</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">Teaching</p>
                            </div>

                            <!-- Detail 5: Duration -->
                            <div class="flex flex-col items-center text-center">
                                <i class="fas fa-users text-primary text-lg sm:text-xl mb-1 sm:mb-2"></i>
                                <p class="text-white text-[10px] sm:text-xs font-medium">In Person</p>
                                <p class="text-white/70 text-[10px] sm:text-xs">On-site only</p>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="text-center">
                        <a href="https://docs.google.com/forms/d/e/1FAIpQLSeJ_pwMI9MZgc4j91BQa_uDG7fgsIiKRH_OOJjsLwgkCTpmJQ/viewform?usp=header" target="_blank" id="modalEnrollButton" class="bg-primary hover:bg-primary/90 text-black font-bold text-sm sm:text-base px-6 sm:px-8 md:px-10 py-3 sm:py-4 inline-flex items-center transition-all duration-300">
                            <!-- <i class="fas fa-user-plus mr-2"></i> -->
                            Join the Waiting List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollment Modal Script -->
    <script>
        // Initialize the enrollment modal functionality
        (function() {
            // Show modal with fade-in animation after 6 seconds
            setTimeout(function() {
                const modal = document.getElementById('enrollmentModal');
                const modalContent = document.getElementById('modalContent');

                if (modal) {
                    // First make it visible but still transparent
                    modal.classList.remove('hidden');

                    // Force a reflow to ensure the transition works
                    void modal.offsetWidth;

                    // Fade in the modal backdrop
                    modal.classList.add('opacity-100');

                    // Animate in the modal content
                    setTimeout(function() {
                        modalContent.classList.remove('opacity-0', 'translate-y-8');
                    }, 200);
                }
            }, 6000); // 6 seconds

            // Function to close modal without animation (just hide it immediately)
            function closeModalWithoutAnimation() {
                const modal = document.getElementById('enrollmentModal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            }

            // Add event listeners when DOM is fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                // Close modal when clicking the close button
                const closeBtn = document.getElementById('closeModal');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeModalWithoutAnimation);
                }

                // Close modal when clicking the enroll button
                const enrollBtn = document.getElementById('modalEnrollButton');
                if (enrollBtn) {
                    enrollBtn.addEventListener('click', closeModalWithoutAnimation);
                }

                // Close modal when clicking outside the modal content
                const modal = document.getElementById('enrollmentModal');
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        // Check if the click is on the modal backdrop (outside the modal content)
                        if (e.target === this || e.target.id === 'modalBackdrop') {
                            closeModalWithoutAnimation();
                        }
                    });
                }

                // Add specific listener for the backdrop
                const backdrop = document.getElementById('modalBackdrop');
                if (backdrop) {
                    backdrop.addEventListener('click', closeModalWithoutAnimation);
                }
            });
        })(); // Immediately invoke the function
    </script>

    <!-- Gallery Lightbox Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create lightbox elements
            const lightboxOverlay = document.createElement('div');
            lightboxOverlay.className = 'fixed inset-0 bg-black/90 z-50 hidden flex items-center justify-center';

            const lightboxContent = document.createElement('div');
            lightboxContent.className = 'relative max-w-4xl mx-auto p-4';

            const lightboxImage = document.createElement('img');
            lightboxImage.className = 'max-h-[80vh] max-w-full';

            const closeButton = document.createElement('button');
            closeButton.className = 'absolute top-0 right-0 -mt-12 -mr-12 bg-primary w-10 h-10 rounded-full flex items-center justify-center text-white';
            closeButton.innerHTML = '<i class="fas fa-times"></i>';

            // Append elements
            lightboxContent.appendChild(lightboxImage);
            lightboxContent.appendChild(closeButton);
            lightboxOverlay.appendChild(lightboxContent);
            document.body.appendChild(lightboxOverlay);

            // Add click event to gallery items
            const galleryLinks = document.querySelectorAll('.lightbox-link');
            galleryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('href');
                    lightboxImage.src = imgSrc;
                    lightboxOverlay.classList.remove('hidden');
                    lightboxOverlay.classList.add('flex');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });
            });

            // Close lightbox when clicking close button
            closeButton.addEventListener('click', function() {
                lightboxOverlay.classList.add('hidden');
                lightboxOverlay.classList.remove('flex');
                document.body.style.overflow = ''; // Restore scrolling
            });

            // Close lightbox when clicking outside the image
            lightboxOverlay.addEventListener('click', function(e) {
                if (e.target === lightboxOverlay) {
                    lightboxOverlay.classList.add('hidden');
                    lightboxOverlay.classList.remove('flex');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });

            // Close lightbox with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !lightboxOverlay.classList.contains('hidden')) {
                    lightboxOverlay.classList.add('hidden');
                    lightboxOverlay.classList.remove('flex');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });
        });
    </script>



    <!-- Video preload and caching script -->
    <script>
        // Ensure video is preloaded and cached
        document.addEventListener('DOMContentLoaded', function() {
            const heroVideo = document.querySelector('video');
            if (heroVideo) {
                // Force the browser to cache the video
                heroVideo.load();

                // Add event listeners to monitor video loading
                heroVideo.addEventListener('loadeddata', function() {
                    console.log('Video data loaded successfully');
                });

                heroVideo.addEventListener('error', function(e) {
                    console.error('Error loading video:', e);
                });
            }
        });
    </script>

    <!-- AOS (Animate On Scroll) Initialization -->
    <script src="https://unpkg.com/aos@next/dist/aos.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: false,
                mirror: false,
                offset: 100
            });
        });
    </script>
</body>
</html>