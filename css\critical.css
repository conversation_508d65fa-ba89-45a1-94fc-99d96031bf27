/* Consolidated CSS for Khuddam Website */

/***************************************
 * BASIC ELEMENTS & TYPOGRAPHY
 ***************************************/

/* Basic body styling */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #101010;
    margin: 0;
    padding: 0;
}

/* Arabic text styling */
.font-arabic, .arabic-text, .arabic {
    font-family: 'Amiri', serif;
    direction: rtl;
}

.arabic-text {
    text-align: center;
    font-size: 28px;
    line-height: 1.8;
    color: #deae35;
    margin: 20px 0 30px;
    opacity: 0.9;
    letter-spacing: 1px;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

/* Main content transition */
#main-content {
    transition: opacity 1s ease, visibility 1s ease;
}

/* Loader styles */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #101010;
    z-index: 9999;
    transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
}

.loader-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.book-animation {
    width: 120px;
    height: 120px;
    position: relative;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.book-animation i {
    font-size: 80px;
    color: white;
    animation: bookFloat 3s ease-in-out infinite;
    display: block;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
}

@keyframes bookFloat {
    0% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0); }
}

.arabic-text {
    text-align: center;
    font-size: 28px;
    line-height: 1.8;
    color: #deae35;
    margin: 20px 0 30px;
    opacity: 0.9;
    letter-spacing: 1px;
}

.loader-text {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    text-align: center;
    max-width: 400px;
    line-height: 1.5;
    margin-bottom: 30px;
    font-style: italic;
}

.loader-progress {
    position: absolute;
    bottom: 50px;
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 0%;
    background: #deae35;
    transition: width 0.3s ease;
}

.pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.png');
    background-size: 300px;
    opacity: 0.03;
    pointer-events: none;
}

/* Header styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transition: all 0.3s;
    background-color: rgba(16, 16, 16, 0.95);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile menu styles */
#mobile-menu {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

#mobile-menu.hidden {
    display: none;
}

#mobile-menu:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

/* Hero section styles */
.hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 4rem;
    background-color: #000;
    overflow: hidden;
}

/* Floating animation for scroll indicator */
@keyframes floating {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.floating-animation {
    animation: floating 3s ease-in-out infinite;
}

/* Color variables */
:root {
    --primary: #deae35;
    --dark: #101010;
}

/* Utility classes used in above-the-fold content */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.text-primary {
    color: #deae35;
}

.text-white {
    color: #ffffff;
}

.text-gray-300 {
    color: #d1d5db;
}

.bg-black {
    background-color: #000000;
}

.bg-dark\/95 {
    background-color: rgba(16, 16, 16, 0.95);
}

.border-primary {
    border-color: #deae35;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .md\:text-8xl {
        font-size: 6rem;
        line-height: 1;
    }

    .md\:text-5xl {
        font-size: 3rem;
        line-height: 1;
    }

    .md\:hidden {
        display: none;
    }

    .md\:flex {
        display: flex;
    }
}

@media (max-width: 767px) {
    .hidden.md\:flex {
        display: none;
    }
}

/* Gradient animation for enrollment section */
.gradient-animation {
    background: linear-gradient(-45deg,
        #0f172a, /* Deep blue */
        #1e293b, /* Navy blue */
        #1a1a1a, /* Dark gray */
        #1e293b, /* Navy blue */
        #834646  /* Deep blue */
    );
    opacity: 0.9;
}

/***************************************
 * COMPONENT STYLES
 ***************************************/

/* Hero content styles */
.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.75rem;
    font-weight: 300;
    color: #8FB996;
    margin-bottom: 0.5rem;
}

.hero-subtitle-bold {
    font-size: 1.75rem;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.hero-text {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.75rem;
}

.hero-statement {
    font-size: 2.5rem;
    font-weight: 700;
    color: #b3d234;
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

/* Button styles */
.btn-hero-primary {
    border: 2px solid #b3d234;
    color: #b3d234;
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-hero-primary:hover {
    background-color: #b3d234;
    color: #000000;
    transform: translateY(-2px);
}

.btn-hero-secondary {
    border: 2px solid #ffffff;
    color: #ffffff;
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
    background-color: #ffffff;
    color: #000000;
    transform: translateY(-2px);
}

.btn-primary {
    background-color: #1F4E5F;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #91b250;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: #1F4E5F;
    border: 2px solid #1F4E5F;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #1F4E5F;
    color: white;
    transform: translateY(-2px);
}

/* Card and box styles */
.quote-box {
    position: relative;
    padding: 2rem;
    border-radius: 0.5rem;
    background-color: rgba(143, 185, 150, 0.1);
    border-left: 4px solid #91b250;
}

.quote-box::before {
    content: '"';
    font-family: 'Poppins', sans-serif;
    position: absolute;
    top: 0;
    left: 1rem;
    font-size: 4rem;
    color: rgba(143, 185, 150, 0.3);
    line-height: 1;
}

/* Glassy card styles */
.glass-card {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(
        to right,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transform: rotate(30deg);
    pointer-events: none;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px -5px rgba(31, 38, 135, 0.25),
                inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Gradient backgrounds for cards */
.gradient-primary {
    background: linear-gradient(135deg, rgba(31, 78, 95, 0.6) 0%, rgba(31, 78, 95, 0.3) 100%);
    border-top: 1px solid rgba(31, 78, 95, 0.3);
    border-left: 1px solid rgba(31, 78, 95, 0.3);
}

.gradient-secondary {
    background: linear-gradient(135deg, rgba(143, 185, 150, 0.6) 0%, rgba(143, 185, 150, 0.3) 100%);
    border-top: 1px solid rgba(143, 185, 150, 0.3);
    border-left: 1px solid rgba(143, 185, 150, 0.3);
}

.gradient-accent {
    background: linear-gradient(135deg, rgba(230, 184, 156, 0.6) 0%, rgba(230, 184, 156, 0.3) 100%);
    border-top: 1px solid rgba(230, 184, 156, 0.3);
    border-left: 1px solid rgba(230, 184, 156, 0.3);
}

.gradient-light {
    background: linear-gradient(135deg, rgba(249, 247, 243, 0.8) 0%, rgba(249, 247, 243, 0.5) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}

/* Custom card hover effects */
.resource-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile Menu additional styles */
#mobile-menu-close {
    cursor: pointer;
    padding: 10px;
    z-index: 60;
}

#mobile-menu a {
    position: relative;
    padding: 0.75rem 1.5rem;
    display: inline-block;
    text-align: center;
    width: 100%;
}

#mobile-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #deae35; /* Updated to primary color */
    transition: width 0.3s ease, left 0.3s ease;
}

#mobile-menu a:hover::after {
    width: 60%;
    left: 20%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #101010;
}

::-webkit-scrollbar-thumb {
    background: #deae35;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #c99b2d;
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: #deae35 #101010;
}
