# 📸 Event Creation Guide - Khuddam Gallery System

## 🎯 Overview
This guide will walk you through creating a new event and its dedicated gallery page step by step. The gallery system uses an event-based approach where each event has its own collection of photos.

## 📁 System Structure
```
js/events-posts.js             # Main events data file
events.html                    # Main events overview page
event-pages/
├── event-name.html            # Individual event gallery pages
└── event-images/            # Photo storage folder
```

---

## 🚀 Step 1: Add Event Data

### 1.1 Open `js/events-posts.js`
This file contains all event information and photos.

### 1.2 Add Your New Event
Add your event to the `events` array at the **beginning** (so it appears first):

```javascript
const events = [
    {
        id: 5,                                    // Next available ID number
        title: "Your Event Title",                // Event name
        date: "2025-06-15",                      // Date in YYYY-MM-DD format
        location: "Event Location",               // Where it took place
        description: "Brief description of the event and what happened.",
        coverImage: "./event-pages/event-images/your-cover-image.webp",
        photoCount: 12,                          // Total number of photos
        slug: "your-event-slug",                 // URL-friendly name (no spaces)
        category: "Conference",                   // Event category (see categories below)
        photos: [
            { id: 1, image: "event-images/your-event (1).webp" },
            { id: 2, image: "event-images/your-event (2).webp" },
            { id: 3, image: "event-images/your-event (3).webp" },
            // Add all your photos here...
        ]
    },
    // ... existing events below
];
```

### 1.3 Event Categories
Choose from these categories (affects badge color):
- **Conference** - Blue badge
- **Class** - Green badge
- **Community** - Purple badge
- **Program** - Orange badge

---

## 📷 Step 2: Prepare Your Photos

### 2.1 Photo Organization
1. Create a folder named after your event (e.g., `YourEvent2025`)
2. Name photos consistently: `YourEvent2025 (1).webp`, `YourEvent2025 (2).webp`, etc.
3. Choose one photo as your cover image

### 2.2 Photo Requirements
- **Format**: `.webp` preferred (smaller file size)
- **Naming**: Use consistent numbering in parentheses
- **Quality**: High resolution but optimized for web
- **Cover Image**: Should be representative of the event

### 2.3 Upload Photos
Place all photos in the `event-pages/event-images/` folder.

---

## 🌐 Step 3: Create Gallery Page

### 3.1 Copy Template
1. Go to the `event-pages/` folder
2. Copy an existing event page (e.g., `arabic-conference-2024.html`)
3. Rename it to match your slug: `your-event-slug.html`

### 3.2 Update Page Content
Open your new HTML file and update these sections:

#### A. Page Title & Meta (Lines 6-7)
```html
<title>Your Event Title - Khuddam Gallery</title>
<meta name="description" content="Photos from Your Event Title at Event Location. Brief description.">
```

#### B. Event Header (Lines 165-181)
```html
<h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
    Your Event Title
</h1>
<div class="flex flex-wrap items-center justify-center gap-6 text-white/90">
    <div class="flex items-center">
        <i class="fas fa-calendar mr-2 text-primary"></i>
        <span>June 15, 2025</span>
    </div>
    <div class="flex items-center">
        <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
        <span>Event Location</span>
    </div>
</div>
<p class="text-lg text-white/80 max-w-2xl mx-auto">
    Brief description of the event and what happened.
</p>
```

#### C. Event ID (Line 247)
```javascript
const eventId = 5; // Your new event ID (must match js/events-posts.js)
```

---

## ⚙️ Step 4: Configure Settings

### 4.1 Photos Per Load
Adjust how many photos load at once (Line 250):
```javascript
let photosPerLoad = 12; // Recommended: 6-16 depending on event size
```

### 4.2 Verify Event ID
Make sure the `eventId` in your HTML file matches the `id` in `js/events-posts.js`.

---

## ✅ Step 5: Test Your Event

### 5.1 Check Main Gallery
1. Open `gallery.html` in your browser
2. Verify your event card appears first
3. Check that the cover image, title, and photo count are correct

### 5.2 Test Individual Gallery
1. Click on your event card
2. Verify it opens your new gallery page
3. Test photo loading and modal popups
4. Check that all photos display correctly

### 5.3 Test Features
- ✅ Infinite scroll loading
- ✅ Modal popup with navigation
- ✅ Photo counter
- ✅ Responsive design on mobile

---

## 🔧 Troubleshooting

### Photos Not Loading?
- Check file paths in `js/events-posts.js`
- Ensure photos are in `event-pages/event-images/` folder
- Verify photo file names match exactly

### Event Not Appearing?
- Check that event is added to the beginning of the `events` array
- Verify the event has a unique ID
- Make sure `photoCount` matches actual number of photos

### Gallery Page Not Working?
- Verify `eventId` matches between HTML file and `js/events-posts.js`
- Check that the HTML file is in the `event-pages/` folder
- Ensure the slug in the filename matches the event data

---

## 📝 Quick Checklist

Before going live, verify:
- [ ] Event added to `js/events-posts.js` with unique ID
- [ ] All photos uploaded to `event-pages/event-images/`
- [ ] Cover image path is correct
- [ ] Photo count matches actual number of photos
- [ ] HTML file created with correct slug name
- [ ] Event ID updated in HTML file
- [ ] Page title and description updated
- [ ] Event details (date, location, description) updated
- [ ] Tested on main gallery page
- [ ] Tested individual gallery functionality

---

## 🎉 You're Done!

Your new event and gallery page are now live! Visitors can browse your event from the main gallery page and view all photos with the infinite scroll feature.

**Need help?** Check the existing events in `js/events-posts.js` for reference examples.
