// Default Page Load Loader with smooth fade-in transition
document.addEventListener('DOMContentLoaded', function() {
    const loader = document.getElementById('loader');
    const progress = document.getElementById('progress');
    const mainContent = document.getElementById('main-content');

    // Check if loader has already been shown in this session
    const hasSeenLoader = sessionStorage.getItem('loaderShown');

    // If loader was already shown, skip it
    if (hasSeenLoader) {
        if (loader) {
            loader.style.display = 'none';
        }
        if (mainContent) {
            mainContent.style.opacity = '1';
            mainContent.style.visibility = 'visible';
        }
        return; // Exit early, don't run loader
    }

    let isPageLoaded = false;
    let progressValue = 0;
    const minDisplayTime = 500; // Minimum 500ms to show loader
    const startTime = Date.now();

    // Hide the main content initially
    if (mainContent) {
        mainContent.style.opacity = '0';
        mainContent.style.visibility = 'hidden';
        // Add transition for smoother fade-in
        mainContent.style.transition = 'opacity 1s ease-in-out, visibility 1s ease-in-out';
    }

    // Function to hide loader and show content
    function hideLoader() {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        setTimeout(() => {
            // Ensure progress is at 100%
            if (progress) {
                progress.style.width = '100%';
            }

            // Add transition to loader for smooth fade-out
            if (loader) {
                loader.style.transition = 'opacity 0.8s ease-out, visibility 0.8s ease-out';
            }

            // Hide loader and show main content
            setTimeout(() => {
                if (loader) {
                    loader.classList.add('hidden');
                }

                if (mainContent) {
                    // Apply smooth fade-in
                    mainContent.style.opacity = '1';
                    mainContent.style.visibility = 'visible';

                    // Initialize Locomotive Scroll if it exists
                    if (window.locomotiveScroll) {
                        setTimeout(() => {
                            window.locomotiveScroll.update();
                        }, 300);
                    }
                }

                // Mark loader as shown for this session
                sessionStorage.setItem('loaderShown', 'true');
            }, 100);
        }, remainingTime);
    }

    // Simulate progress based on typical loading events
    function updateProgress() {
        if (!isPageLoaded) {
            // Gradually increase progress while page is loading
            progressValue = Math.min(progressValue + Math.random() * 3, 90);

            if (progress) {
                progress.style.width = progressValue + '%';
            }

            // Continue updating until page loads
            setTimeout(() => {
                requestAnimationFrame(updateProgress);
            }, 50 + Math.random() * 100); // Random interval between 50-150ms
        }
    }

    // Start progress simulation
    updateProgress();

    // Listen for page load completion
    window.addEventListener('load', function() {
        isPageLoaded = true;

        // Complete the progress bar
        progressValue = 100;
        if (progress) {
            progress.style.width = '100%';
        }

        // Hide loader after page is fully loaded
        hideLoader();
    });

    // Fallback: Hide loader after maximum time even if load event doesn't fire
    setTimeout(() => {
        if (!isPageLoaded) {
            isPageLoaded = true;
            progressValue = 100;
            if (progress) {
                progress.style.width = '100%';
            }
            hideLoader();
        }
    }, 10000); // 10 second maximum
});
