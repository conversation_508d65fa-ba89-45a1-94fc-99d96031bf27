# Khuddam - Servants of the Quran Website

## 🌟 Overview

**Khuddam** (خُدَّام الْقُرآن) is a modern, responsive website for an Islamic educational organization focused on Quranic Arabic Grammar Learning. The website serves as a platform for course enrollment, community engagement, and educational outreach.

### 🎯 Mission
Deepening participants' connection to the Quran through linguistic tools and community building, making the divine message accessible to all Muslims regardless of their background.

## 🚀 Features

### ✨ Core Functionality
- **Multi-step Registration Form** with Google Forms integration
- **Contact Form** with automated email responses
- **Responsive Design** optimized for all devices
- **Progressive Web App (PWA)** capabilities
- **Multi-language Support** (English/Arabic)
- **SEO Optimized** with structured data
- **Performance Optimized** with lazy loading and caching

### 🎨 Design Features
- **Modern UI/UX** with Tailwind CSS
- **Islamic Aesthetic** with Arabic typography
- **Video Background** hero section
- **Smooth Animations** using AOS library
- **Loading Screen** with Islamic greeting
- **Dark/Light Theme** support

## 📁 Project Structure

```
khuddam-website/
├── index.html              # Homepage
├── about.html              # About Us page
├── contact-form.html       # Contact page with enhanced FAQ
├── registration-form.html  # Registration page
├── news.html               # News posts page with modal popups
├── js/news-posts.js        # News posts management and modal logic
├── css/
│   └── critical.css        # Critical CSS styles
├── js/
│   ├── main.js            # Main JavaScript functionality
│   └── loader.js          # Loading screen logic
├── images/                # Image assets
│   ├── logos/
│   ├── backgrounds/
│   ├── gallery/           # Class photos and gallery images
│   ├── news-images/       # Community post images
│   └── icons/
├── 30fps.mp4             # Hero video background
├── sw.js                 # Service Worker for PWA
├── robots.txt            # SEO robots file
├── sitemap.xml           # XML sitemap
├── README.md             # Project documentation
└── .htaccess             # Apache configuration
```

## 🛠️ Technology Stack

### Frontend
- **HTML5** - Semantic markup with accessibility features
- **CSS3** - Modern styling with Flexbox/Grid
- **JavaScript (ES6+)** - Interactive functionality
- **Tailwind CSS** - Utility-first CSS framework

### External Libraries
- **Font Awesome 6.4.0** - Icons
- **AOS (Animate On Scroll)** - Scroll animations
- **Google Fonts** - Amiri (Arabic) & Poppins typography

### Backend Integration
- **Google Forms** - Form submissions and data collection
- **Google Analytics** - Website analytics
- **Service Worker** - Offline functionality and caching

## 📱 Pages Overview

### 🏠 Homepage (`index.html`)
- **Hero Section** with video background
- **Program Overview** with enrollment information
- **Testimonials** carousel
- **Call-to-Action** sections
- **Enrollment Modal** popup

### ℹ️ About Us (`about.html`)
- **Organization Mission** and values
- **Team Information** with instructor profiles
- **Our Journey** section with background image
- **Program Details** and methodology

### 📞 Contact (`contact-form.html`)
- **Contact Information** with location details
- **Contact Form** with Google Forms integration
- **Response Time** indicators
- **Social Media** links

### 📝 Registration (`registration-form.html`)
- **Multi-step Form** (2 steps)
- **Step 1**: Personal details and motivation
- **Step 2**: WhatsApp group joining
- **Thank You Modal** with home navigation
- **Form Validation** and error handling

### 📰 News (`news.html`)
- **News Posts** display with pagination
- **Dynamic Modal Popups** for post details
- **Responsive Image Display** with natural aspect ratios
- **Post Categories**: Quran classes, lectures, dawah, events, enrollment
- **Instagram-style** post card layout
- **Smooth Hover Effects** with subtle animations

## 🔧 Setup & Installation

### Prerequisites
- Web server (Apache/Nginx)
- Modern web browser
- Internet connection for CDN resources

### Local Development
1. **Clone/Download** the project files
2. **Maintain folder structure** as shown above
3. **Open index.html** in a web browser
4. **Test all forms** and functionality

### Production Deployment
1. **Upload all files** to web server root directory
2. **Configure .htaccess** for Apache servers
3. **Update Google Form URLs** in JavaScript files
4. **Test all pages** and forms on live server
5. **Verify SSL certificate** for HTTPS

## 📋 Configuration

### Google Forms Integration
Update the following form action URLs in the respective files:

**Registration Form** (`registration-form.html`):
```javascript
hiddenForm.action = 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse';
```

**Contact Form** (`contact-form.html`):
```javascript
hiddenForm.action = 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse';
```

### Form Field Mapping
**Registration Form Fields**:
- Full Name: `entry.496392723`
- Email: `entry.2056515345`
- Phone: `entry.1435714806`
- Motivation: `entry.1984046647`
- Comments: `entry.1578709933`

**Contact Form Fields**:
- First Name: `entry.1234567890`
- Last Name: `entry.0987654321`
- Email: `entry.1122334455`
- Subject: `entry.5566778899`
- Message: `entry.9988776655`

### SEO Configuration
Update the following in each HTML file:
- **Meta descriptions**
- **Open Graph tags**
- **Twitter Card data**
- **Structured data (JSON-LD)**
- **Canonical URLs**

## 🎨 Customization

### Colors (Tailwind Config)
```javascript
colors: {
    primary: '#deae35',    // Gold/Yellow
    secondary: '#606060',  // Gray
    light: '#F9F7F3',     // Light cream
    dark: '#101010'       // Dark black
}
```

### Fonts
- **Arabic Text**: Amiri (Google Fonts)
- **English Text**: Poppins (Google Fonts)

### Images
Replace images in the `/images/` folder:
- **Logo**: `old-logo-border.png`
- **Hero Background**: `image1.webp`
- **About Background**: `foldpages.avif`
- **Team Photos**: `teacher1.jpeg`, etc.

## 📊 Performance Features

### Optimization
- **Critical CSS** inlined for faster rendering
- **Lazy Loading** for images and videos
- **Service Worker** for offline caching
- **Preload** directives for key resources
- **Minified** CSS and JavaScript

### PWA Features
- **Service Worker** (`sw.js`)
- **Offline Functionality**
- **Cache Strategy** for static assets
- **Mobile App-like** experience

## 🔒 Security Features

### Form Security
- **Client-side Validation**
- **CSRF Protection** via Google Forms
- **Input Sanitization**
- **Rate Limiting** considerations

### General Security
- **HTTPS Enforcement**
- **Content Security Policy** headers
- **XSS Protection**
- **Secure Headers** in .htaccess

## 📱 Browser Support

### Supported Browsers
- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+
- **Mobile Browsers** (iOS Safari, Chrome Mobile)

### Fallbacks
- **Video Fallback** to static image
- **JavaScript Fallback** for form submissions
- **CSS Fallback** for older browsers

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Test all forms and submissions
- [ ] Verify Google Forms integration
- [ ] Check responsive design on all devices
- [ ] Validate HTML/CSS
- [ ] Test loading speeds
- [ ] Verify all links work

### Post-Deployment
- [ ] Test live forms
- [ ] Check SSL certificate
- [ ] Verify Google Analytics
- [ ] Test PWA functionality
- [ ] Check SEO meta tags
- [ ] Monitor error logs

## 📞 Support & Maintenance

### Regular Updates
- **Content Updates**: Course information, dates, etc.
- **Security Updates**: Dependencies and libraries
- **Performance Monitoring**: Loading speeds and user experience
- **Form Testing**: Regular submission testing

### Troubleshooting
- **Form Issues**: Check Google Forms URLs and entry IDs
- **Loading Issues**: Verify CDN resources and caching
- **Mobile Issues**: Test responsive design and touch interactions
- **Performance Issues**: Check image optimization and caching

## 📋 Version History

### 🚀 Version 5.3 (Latest) - Navigation Rebranding & Footer Consistency
**Release Date**: January 28, 2025

#### 📰 Community to News Rebranding
- **File Rename**: Renamed `community.html` to `News.html` for better content clarity
- **Navigation Updates**: Updated all navigation links across the entire website:
  - Header navigation menus (desktop and mobile)
  - Footer navigation links
  - Homepage CTA sections
  - Gallery page navigation
- **Content Updates**:
  - Page title changed from "Community" to "News"
  - Hero section heading updated to "News Updates"
  - Homepage CTA section: "Community Posts" → "News Posts"
  - Homepage CTA button: "View Community" → "View News"

#### 🔗 Comprehensive Link Updates
- **Main Pages Updated**: index.html, about.html, contact-form.html, registration-form.html, events.html
- **Gallery Pages Updated**: All 5 gallery HTML files in event-pages/ folder
- **Meta Tags Updated**: Open Graph and Twitter Card meta tags updated with new URLs and titles
- **Documentation Updated**: README.md file structure and feature descriptions updated

#### 🦶 Footer Consistency Improvements
- **Index.html Footer Fix**: Added missing navigation links to match other pages
- **Complete Navigation**: Footer now includes all pages: Home, About Us, News, Events, Register Now, Contact
- **Active State Styling**: Proper highlighting of current page in footer navigation
- **Consistent Structure**: All pages now have identical footer layout and functionality

#### 🎯 User Experience Enhancements
- **Unified Navigation**: Consistent navigation experience across all pages
- **Clear Content Categorization**: "News" better represents the content type than "Community"
- **No Broken Links**: All internal links properly updated to new filename
- **Improved SEO**: Updated meta tags and structured data for better search engine optimization

#### 🔧 Technical Improvements
- **File Structure Consistency**: Maintained all existing functionality while updating references
- **Gallery Footer Cleanup**: Confirmed gallery pages already had no footers as intended
- **Cross-Page Validation**: Verified all navigation links work correctly across the entire site
- **Documentation Accuracy**: Updated README to reflect actual implemented features

### 🚀 Version 5.2 - Community Modal Enhancements
**Release Date**: January 25, 2025

#### 🖼️ Dynamic Modal Popup Improvements
- **Image-Based Width Sizing**: Modal width now dynamically adjusts based on actual image dimensions for optimal display
- **Responsive Constraints**: Smart sizing system that adapts to different screen sizes:
  - **Mobile (≤640px)**: 95% of viewport width for optimal mobile experience
  - **Tablet (641px-1024px)**: 85% of viewport width for balanced tablet viewing
  - **Desktop (≥1025px)**: Maximum 800px width or 60% of viewport (whichever is smaller) for comfortable reading
- **Natural Image Display**: Images now display in their original aspect ratios without artificial constraints
- **Enhanced User Experience**: Eliminated awkward white space and cramped image display issues

#### 🔧 Technical Modal Improvements
- **Smart Width Calculation**: JavaScript dynamically calculates optimal modal width (image width + padding)
- **Image Loading Optimization**: Efficient image dimension detection using native Image() constructor
- **Responsive Breakpoint System**: Intelligent constraints based on viewport size for optimal viewing
- **Clean State Management**: Modal width resets properly when closed for consistent behavior
- **Performance Optimized**: Minimal overhead with efficient image loading and sizing calculations

#### 🎯 User Experience Benefits
- **Better Readability**: Desktop modals no longer expand excessively, maintaining comfortable reading distance
- **Mobile-First Design**: Preserved excellent mobile experience while improving desktop usability
- **No Scrolling Required**: Content fits comfortably within viewport on all screen sizes
- **Natural Proportions**: Images display in their intended aspect ratios for better visual appeal
- **Consistent Behavior**: Reliable modal sizing across all devices and image types

### 🚀 Version 5.1 - Content Updates & UI Enhancements
**Release Date**: January 25, 2025

#### 📞 Contact Information Updates
- **Phone Number Update**: Updated contact phone number across all pages from `+64 21 123 4567` to `+64 2757 93314`
- **Comprehensive Update**: Updated phone numbers in:
  - Contact form display sections
  - JSON-LD structured data for SEO
  - Contact information cards
  - Registration form placeholders
  - All HTML pages (index.html, contact-form.html, about.html, registration-form.html)

#### ⏰ Operating Hours Update
- **New Schedule**: Updated operating hours to reflect actual Khuddam operations:
  - **Monday-Friday**: 6:00 PM - 10:00 PM (evening classes for working adults)
  - **Saturday**: Closed
  - **Sunday**: 6:00 AM - 8:00 AM at Masjid Ar Rahman, Carr Road, Auckland
- **Multi-Location Support**: Added specific location information for Sunday classes
- **SEO Updates**: Updated structured data across all pages for search engine optimization

#### 🎨 FAQ Interactive Enhancements
- **Visual Feedback**: Enhanced FAQ section with dynamic styling:
  - Active question text changes to primary color when opened
  - FAQ item border changes to primary color when active
  - Smooth color transitions for better user experience
- **Smooth Animations**: Implemented fluid expand/collapse animations:
  - Replaced instant show/hide with smooth max-height transitions
  - Added 500ms ease-in-out animations for content reveal
  - Enhanced chevron icon rotation with smooth transitions
- **Improved UX**: Better visual indication of active/inactive FAQ states

#### 🔧 Technical Improvements
- **Service Worker Optimization**: Cleaned cache entries for removed poster functionality
- **Performance Enhancement**: Removed unused CSS and JavaScript files
- **Code Cleanup**: Eliminated dead code and unused references
- **Responsive Design**: Maintained mobile-first approach across all updates

### 🚀 Version 5.0 - Enhanced Registration Experience
**Release Date**: January 2025

#### ✨ New Features
- **Dynamic Form Title**: Title changes from "Register in 2 Steps" to "One Final Step" when user reaches Step 2
- **Streamlined Registration Flow**: Removed redundant "Submit Registration" button for cleaner user experience
- **Automatic Registration Completion**: WhatsApp button automatically changes to "Registration Complete" when user returns from WhatsApp
- **Smart Return Detection**: Multi-layered detection system for desktop and mobile devices
- **Auto Thank You Modal**: Automatically displays thank you popup when users return from WhatsApp group
- **Mobile App Integration**: Seamless WhatsApp app switching on iOS and Android devices
- **Cross-Platform Compatibility**: Works reliably across all major browsers and mobile platforms

#### 🔧 Technical Improvements
- **Enhanced Scroll Behavior**: Fixed scroll positioning to show Step 2 from the page title section
- **Improved State Management**: Better localStorage handling for tracking registration progress
- **Multi-Event Detection System**: Combines `focus`, `visibilitychange`, and `pageshow` events for reliable mobile detection
- **Periodic Fallback Checking**: Smart interval-based detection for mobile browsers with limited event support
- **Battery Optimization**: Automatic cleanup of detection processes after 5 minutes to preserve device battery
- **Optimized User Flow**: Seamless transition between form steps and WhatsApp integration
- **Better Visual Feedback**: Clear indication of registration completion status

#### 🎯 User Experience Enhancements
- **Clearer Progress Indication**: Dynamic title reflects current step status
- **Reduced Friction**: Eliminated unnecessary button clicks in registration process
- **Automatic Completion**: No manual action required after joining WhatsApp group
- **Persistent State**: Registration state maintained across browser sessions
- **Mobile-First Design**: Optimized for native app switching on smartphones and tablets
- **Universal Compatibility**: Consistent experience across iOS Safari, Android Chrome, and desktop browsers
- **Improved Accessibility**: Better scroll positioning starting from page title section
- **Smart Detection**: Works reliably when users return from WhatsApp app on any device

#### 🐛 Bug Fixes
- Fixed scroll positioning issues when transitioning to Step 2
- Resolved form state management inconsistencies
- Corrected button text updates for WhatsApp integration
- Fixed localStorage cleanup on form reset
- **Mobile Detection Fix**: Resolved window focus detection not working on iOS and Android devices
- **App Switching Issues**: Fixed detection when users return from WhatsApp app on mobile browsers
- **Cross-Browser Compatibility**: Ensured consistent behavior across all mobile browsers and platforms

### 📋 Previous Versions
- **v4.x**: Multi-step registration form with WhatsApp integration
- **v3.x**: Contact form enhancements and SEO optimization
- **v2.x**: Responsive design improvements and PWA features
- **v1.x**: Initial website launch with basic functionality

## 📄 License

© 2025 Khuddam - Servants of the Quran. All rights reserved. This project is proprietary software for Khuddam - Servants of the Quran organization.

## 👥 Credits

- **Design & Development**: [Kiwiorbit](https://kiwiorbit.vercel.app/) - Full-stack web development
- **Developer Contact**: +64 22 325 9094
- **Icons**: Font Awesome
- **Fonts**: Google Fonts (Amiri, Poppins)
- **Framework**: Tailwind CSS
- **Animations**: AOS Library

---

**For technical support or questions, please contact:**
- **Kiwiorbit Development Team**: [https://kiwiorbit.vercel.app/](https://kiwiorbit.vercel.app/)
- **Phone**: +64 22 325 9094
